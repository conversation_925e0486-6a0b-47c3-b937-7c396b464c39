import React from "react";
import { But<PERSON> } from "@/components/button/button";
import classroomBackgroundImage from "@/assets/images/classroomBackgroundImage.png";
import micOn from "@/assets/svgs/micOn.svg";
import micOff from "@/assets/svgs/micOff.svg";
import videoOn from "@/assets/svgs/videoOn.svg";
import videoOff from "@/assets/svgs/videoOff.svg";
import { capitalizeWords } from "@/utils";
import { format, parseISO } from "date-fns";

const JoinRoom = ({
  isMuted,
  toggleMic,
  isVideoOn,
  toggleVideo,
  micIcon,
  generatingToken,
  enterRoom,
  classDetails,
}) => {
  const formattedDate = classDetails?.scheduledTime
    ? format(parseISO(classDetails.scheduledTime), "EEEE, MMMM d, yyyy")
    : "";
  const formattedTime = classDetails?.scheduledTime
    ? format(parseISO(classDetails.scheduledTime), "h:mm a")
    : "";
  const duration = classDetails?.duration || 0;

  return (
    <div className="lg:flex gap-8 items-center h-full w-full sm:p-8 p-4">
      <div className="lg:max-w-[65%] w-full overflow-hidden rounded-xl">
        <div className="relative">
          <div className="absolute inset-0 bg-black/40 z-0" />
          <img
            src={classroomBackgroundImage}
            alt="tutor background image"
            className="w-full h-full lg:max-h-[600px] md:max-h-[450px]"
          />
          <h3 className="absolute bottom-7 left-7 text-white sm:text-2xl text-xl font-bold">
            {capitalizeWords(classDetails?.tutor?.name)}
          </h3>
        </div>

        <div className="flex gap-5 justify-center items-center bg-black text-white p-5">
          <div className="flex flex-col justify-center items-center">
            <img
              id="mic-icon"
              onClick={toggleMic}
              src={isMuted ? micOn : micOff}
              alt="mic icon"
              className="cursor-pointer max-w-6"
            />
            <p>Microphone</p>
          </div>
          <div className="flex flex-col justify-center items-center">
            <img
              id="video-icon"
              className="cursor-pointer max-w-6"
              src={isVideoOn ? videoOff : videoOn}
              onClick={toggleVideo}
              alt="video icon"
            />
            <p>Video</p>
          </div>
        </div>
      </div>

      <form
        className="w-full lg:max-w-[35%] lg:p-10 max-lg:mt-10"
        id="form"
        onSubmit={enterRoom}
      >
        <h2 className="text-secondary sm:text-2xl text-xl font-bold text-center mb-7">
          {classDetails?.title} with{" "}
          {capitalizeWords(classDetails?.tutor?.name)}
        </h2>

        <p className="text-center mb-4 text-gray-600">
          {formattedDate} at {formattedTime} ({duration} minutes)
        </p>

        <Button type="submit" className="w-full" disabled={generatingToken}>
          {generatingToken ? "Joining..." : "Join Now"}
        </Button>
      </form>
    </div>
  );
};

export default JoinRoom;
