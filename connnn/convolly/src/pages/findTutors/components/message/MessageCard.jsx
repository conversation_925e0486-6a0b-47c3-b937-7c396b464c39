import { Id<PERSON><PERSON>, X } from "lucide-react";
import React, { useState } from "react";
import img from "../../../../assets/images/tutor1.png";
import { Button } from "@/components/button/button";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const MessageCard = ({ onClose, tutor }) => {
  const [message, setMessage] = useState("");
  const [sending, setSending] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const navigate = useNavigate();

  // Get current user from Redux
  const currentUser = useSelector((state) => state?.app?.userInfo?.user);
  const accessToken = useSelector((state) => state?.app?.userInfo?.accessToken);

  const onSubmit = async (data) => {
    if (!data.message.trim() || !tutor || !currentUser) return;

    setSending(true);
    try {
      // Navigate to messaging page with the tutor
      // This will create a new conversation if one doesn't exist
      navigate(
        `/${currentUser.role}/messages/${tutor.id}?message=${encodeURIComponent(
          data.message
        )}`
      );

      if (onClose) onClose();
    } catch (error) {
      console.error("Error starting conversation:", error);
      alert("Failed to send message. Please try again.");
    } finally {
      setSending(false);
    }
  };

  const tutorName = tutor
    ? `${tutor.firstname || tutor.firstName || ""} ${
        tutor.lastname || tutor.lastName || ""
      }`.trim()
    : "Tutor";
  const studentName = currentUser
    ? `${currentUser.firstname || currentUser.firstName || ""}`
    : "Student";

  return (
    <div className="sm:w-[669px] w-sm mx-auto p-3 sm:p-6 bg-white rounded-lg shadow-lg">
      <div className="flex gap-[145px] pt-4 justify-between">
        <p className="text-2xl font-bold text-[#1A1A40]">Send Message</p>
        <button onClick={onClose} disabled={sending}>
          <X />
        </button>
      </div>
      <div className="px-1 sm:px-2 min-h-[552px]">
        <div className="gap-3 ">
          <div className="flex justify-start my-4">
            <div className="">
              <img
                src={tutor?.image || tutor?.profilePicture || img}
                alt="tutor logo"
                className="w-24 h-24 rounded-full object-cover"
              />
            </div>
            <p className="text-lg sm:text-[26px] px-2 text-[#1A1A40]">
              {tutorName}
            </p>
          </div>
          <div className="pb-4 text-[#4B5563] sm:font-medium text-xs sm:text-[18px]">
            <div className="">
              <p className="mb-1">Hi {studentName}</p>
              <p className=" pb-4  font-medium ">
                I hope everything is going well for you. Thanks for looking at
                my profile
              </p>
            </div>

            <p className="mt-4 space-y-2">
              Introduce yourself to the teacher, what are your learning goals?
              How can I help you in the best way?
            </p>
          </div>
        </div>
        <div className="mt-2">
          <p className="text-md sm:text-[22px] font-bold text-[#1A1A40]">
            Send a personal message to the teacher
          </p>
          <div className="text-xs sm:text-lg">
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="flex flex-col space-y-2"
            >
              <textarea
                {...register("message", { required: "Message is required" })}
                placeholder="Your Message"
                className="border border-[#E8E8E8] mt-2 rounded-md w-full min-h-[200px] p-3 focus:outline-none"
                disabled={sending}
              />
              {errors.message && (
                <p className="text-red-500 text-sm">{errors.message.message}</p>
              )}
            </form>
          </div>
        </div>
        <div className="mt-6">
          <Button
            onClick={handleSubmit(onSubmit)}
            disabled={sending}
            className="bg-primary border w-full text-white rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {sending ? "Sending..." : "Send Message"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MessageCard;
