import React from "react";
import tutor from "@/assets/images/tutorProfileImage.png";
import studentsIcon from "@/assets/svgs/students.svg";
import fullStar from "@/assets/svgs/fullStar.svg";
import usa from "@/assets/svgs/usa.svg";
import lessonIcon from "@/assets/svgs/lessons.svg";
import favorite from "@/assets/svgs/favorite.svg";
import greenCheck from "@/assets/svgs/greencheck.svg";
import { Button } from "@/components/button/button";
import MessageCard from "../message/MessageCard";

const TutorCard = ({ tutor }) => {
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  const openModal = () => {
    setIsModalOpen(true);
  };
  const closeModal = () => setIsModalOpen(false);

  return (
    <div className="border border-[#E8E8E8] rounded-xl p-3 flex gap-5 mb-5">
      <div className="w-full">
        <div className="md:flex hidden gap-5 items-stretch">
          <img
            src={tutor.image}
            alt="tutor image"
            className="w-full max-w-[290px] max-h-[200px] object-cover rounded-xl"
          />

          <div className="flex w-full gap-5">
            <div className="">
              <div className="flex gap-2 items-center mb-2">
                <h3 className="text-[26px] font-bold">
                  {tutor?.firstname} {tutor?.lastname}
                </h3>
                <img src={greenCheck} alt="verified icon" />
                <img src={usa} alt="country icon" />
              </div>

              {/* subjects  */}
              {tutor?.teachingSubjects?.map((subject) => (
                <div key={subject.id} className="flex flex-wrap mb-3">
                  {subject.qualities.map((q, index) => (
                    <span
                      key={index}
                      className="border border-[#E8E8E8] p-2 rounded-full m-1 text-sm"
                    >
                      {q}
                    </span>
                  ))}
                </div>
              ))}

              <div className="flex items-center gap-1 mb-2">
                <img src={lessonIcon} alt="lessons icon" />
                <span className="text-[#4B5563] mr-5">
                  {tutor?.totalLessons} lessons
                </span>
              </div>

              {/* language level  */}
              {tutor?.languages?.map((lang) => (
                <div key={lang.id} className="flex items-center gap-1">
                  <span className="text-[#4B5563]">{lang.name}: </span>
                  <span className="text-black mr-5">{lang.level}</span>
                </div>
              ))}
            </div>

            <div className="flex gap-5 lg:min-w-[45%] justify-end">
              <div className="flex flex-col">
                <div className="flex gap-2 items-center">
                  <img src={fullStar} alt="star icon" className="w-6 h-6" />
                  <h2 className="font-bold text-2xl">{tutor?.rating}</h2>
                </div>
                <p className="text-[#4B5563] shrink-0">
                  {tutor?.reviews} reviews
                </p>
              </div>

              <div className="flex flex-col">
                <h2 className="font-bold text-2xl">US${tutor?.basePrice}</h2>
                <p className="text-[#4B5563] shrink-0">{}</p>
              </div>

              <img src={favorite} alt="star icon" className="w-6 h-6" />
            </div>
          </div>
        </div>
        {/* mobile view */}
        <div className="md:hidden">
          {isModalOpen && (
            // Show the message modal if isModalOpen is true
            <div className="inset-0 fixed z-50  flex items-center justify-center bg-black bg-opacity-50">
              <div className="w-sm">
                <MessageCard tutor={tutor} onClose={closeModal} />
              </div>
            </div>
          )}
          <div className="flex gap-3 mb-3">
            <img
              src={tutor?.image}
              alt="tutor image"
              className="w-full max-w-[100px] max-h-[120px] object-cover rounded-lg"
            />

            <div className="w-full">
              <div className="flex items-center mb-2">
                <h3 className="text-lg font-bold">
                  {tutor?.firstname} {tutor?.lastname}
                </h3>
                <img src={greenCheck} alt="verified icon" className="w-5 h-5" />
                <img src={usa} alt="country icon" className="w-5 h-5" />
                <img
                  src={favorite}
                  alt="favorite icon"
                  className="w-5 h-5 ml-auto"
                />
              </div>

              <div className="flex gap-3">
                <div className="flex flex-col">
                  <div className="flex gap-2 items-center">
                    <img src={fullStar} alt="star icon" className="w-5 h-5" />
                    <h2 className="font-bold text-lg">4.5</h2>
                  </div>
                  <p className="text-[#4B5563] shrink-0">{tutor?.reviews}</p>
                </div>

                <div className="flex flex-col">
                  <h2 className="font-bold text-lg">US${tutor?.basePrice}</h2>
                  <p className="text-[#4B5563] shrink-0">50-min lesson</p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-wrap mb-3">
            {tutor?.teachingSubjects?.map((subject) => (
              <div key={subject.id} className="flex flex-wrap mb-3">
                {subject.qualities.map((q, index) => (
                  <span
                    key={index}
                    className="border border-[#E8E8E8] p-2 rounded-full m-1 text-sm"
                  >
                    {q}
                  </span>
                ))}
              </div>
            ))}
          </div>

          <div className="flex items-center gap-1 mb-2">
            <img src={lessonIcon} alt="lessons icon" />
            <span className="text-[#4B5563] mr-5">
              {tutor?.totalLessons} lessons
            </span>
          </div>

          <div className="flex items-center gap-1">
            {tutor?.languages?.map((lang) => (
              <div key={lang.id}>
                <span className="text-[#4B5563]">{lang.name}: </span>
                <span className="text-black">{lang.level}</span>
              </div>
            ))}
          </div>
        </div>
        <div>
          <h3 className="sm:text-xl text-secondary font-bold mb-3 mt-5">
            About
          </h3>
          <p className="text-[#4B5563] sm:text-lg mb-3">{tutor?.aboutMe}</p>
          <a href="#" className="underline block mb-5 text-primary font-bold">
            Read more
          </a>
          <Button className="w-full lg:hidden">Book free lesson</Button>
          <button
            onClick={openModal}
            className="w-full mt-2 border-primary border rounded-md py-2 fill-none lg:hidden"
          >
            Send Message
          </button>
        </div>
      </div>

      <div className="lg:max-w-[240px] grow hidden lg:flex flex-col gap-4">
        <video
          src={tutor?.introVideo}
          controls
          className="w-full h-full object-cover rounded-xl"
        />

        <Button className="w-full text-lg">Book free lesson</Button>
        <button
          onClick={openModal}
          className="w-full text-lg border rounded-md px-2 py-1 text-primary border-primary"
        >
          Send Message
        </button>
      </div>
      {isModalOpen && (
        // Show the message modal if isModalOpen is true
        <div className="inset-0 fixed z-50  flex items-center justify-center bg-black bg-opacity-50">
          <div className="sm:w-[669px]">
            <MessageCard tutor={tutor} onClose={closeModal} />
          </div>
        </div>
      )}
    </div>
  );
};

export default TutorCard;
