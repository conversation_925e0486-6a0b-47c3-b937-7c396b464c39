import { capitalizeWords } from "@/utils";
import React from "react";
import { format } from "date-fns";
import userVector from "@/assets/svgs/userVector.svg";
import { useNavigate } from "react-router-dom";

const UpcommingLesson = ({ lesson }) => {
  const date = format(new Date(lesson.scheduledTime), "EEEE, MMMM d, HH:mm");
  const navigate = useNavigate();

  return (
    <div className="border rounded-lg p-3 sm:p-4 md:p-6">
      <div className="flex flex-col sm:flex-row sm:justify-between gap-3 sm:gap-4">
        {/* Main content section */}
        <div className="flex flex-1 min-w-0">
          <img
            src={lesson?.tutorImage || userVector}
            alt="Tutor profile"
            className="object-cover w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex-shrink-0 mr-3 sm:mr-4"
          />
          <div className="min-w-0 flex-1">
            {/* Date and time */}
            <h3 className="text-[#1A1A40] text-sm sm:text-base md:text-lg lg:text-xl font-semibold leading-tight mb-1 sm:mb-2">
              {date}
            </h3>

            {/* Payment info and tutor details */}
            <div className="text-[#4B5563] text-xs sm:text-sm space-y-1 sm:space-y-0">
              <div className="flex flex-wrap items-start gap-1">
                <span className="font-medium">
                  {capitalizeWords(lesson?.tutor?.name)},
                </span>
                <span className="break-words">{lesson?.title}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Button section */}
        <div className="flex sm:flex-shrink-0 sm:items-start">
          <button
            onClick={() =>
              navigate(`/classroom/${lesson.classroom.id}`, {
                state: lesson,
              })
            }
            className="bg-primary hover:bg-primary/90 transition-colors py-2.5 px-4 sm:px-6 rounded-lg text-white text-sm sm:text-base font-medium w-full sm:w-auto sm:min-w-[120px] focus:outline-none focus:ring-2 focus:ring-primary/50"
          >
            Join class
          </button>
        </div>
      </div>
    </div>
  );
};

export default UpcommingLesson;
