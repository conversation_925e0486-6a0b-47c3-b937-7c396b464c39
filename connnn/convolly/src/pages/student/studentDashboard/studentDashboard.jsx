import React, { useState } from "react";
import MainCards from "./components/MainCards";
import useGet from "@/hooks/useGet";
import { useGetClassesQuery } from "@/redux/slices/student/classesApiSlice";
import { Video } from "lucide-react";
import { useNavigate } from "react-router-dom";
import Loader from "@/components/loader/loader";
import userVector from "@/assets/svgs/userVector.svg";
import { capitalizeWords, formatDisplayTime } from "@/utils";

const StudentDashboard = () => {
  const navigate = useNavigate();

  // Generate days and dates for the current week
  const today = new Date();
  const currentDay = today.getDate();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();

  // Generate days and dates for the current week
  const getStartOfWeek = (date) => {
    const day = date.getDay(); // 0 (Sunday) to 6 (Saturday)
    const diff = date.getDate() - day;
    return new Date(date.setDate(diff));
  };

  const startOfWeek = getStartOfWeek(new Date(today));
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  // Generate dates for the current week
  const dates = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(startOfWeek);
    date.setDate(startOfWeek.getDate() + i);
    return {
      day: days[date.getDay()],
      date: date.getDate(),
      fullDate: date.toISOString().split("T")[0],
      month: date.getMonth(),
      year: date.getFullYear(),
      isToday:
        date.getDate() === currentDay &&
        date.getMonth() === currentMonth &&
        date.getFullYear() === currentYear,
    };
  });

  // Set active date to today's date initially
  const [activeDate, setActiveDate] = useState(() => {
    const todayDate = dates.find((d) => d.isToday);
    return todayDate ? todayDate.date : dates[0].date;
  });

  // Fetch bookings data
  const { data: bookings, isLoading } = useGet(useGetClassesQuery, "");

  // Filter upcoming classes for the selected date
  const upcomingClasses =
    bookings?.bookings?.filter((booking) => {
      const bookingDate = new Date(booking.scheduledTime);
      const selectedDate = dates.find((d) => d.date === activeDate);

      if (!selectedDate) return false;

      const now = new Date();

      // Match the day first
      const sameDay =
        bookingDate.getDate() === selectedDate.date &&
        bookingDate.getMonth() === selectedDate.month &&
        bookingDate.getFullYear() === selectedDate.year;

      // Only upcoming if it's in the future
      const inFuture = bookingDate > now;

      return sameDay && inFuture;
    }) || [];

  const getEndTime = (startTime, duration) => {
    const start = new Date(startTime);
    const end = new Date(start.getTime() + duration * 60000);

    // Use UTC methods to avoid timezone issues
    const hours = end.getUTCHours().toString().padStart(2, "0");
    const minutes = end.getUTCMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  };

  const now = new Date();
  const recentClasses = [...(bookings?.bookings || [])]
    .filter((cls) => new Date(cls.scheduledTime) < now)
    .sort((a, b) => new Date(b.scheduledTime) - new Date(a.scheduledTime))
    .slice(0, 5);

  // Calculate stats
  const uniqueTutors = new Set(recentClasses.map((cls) => cls.tutor.id));
  const totalClasses = bookings?.bookings?.length || 0;
  const totalTutors = uniqueTutors.size;

  const dashbaordStats = {
    totalClasses,
    totalTutors,
    subscribedTutors: "null",
  };

  return (
    <div className="w-full">
      {isLoading && <Loader />}
      <div className="flex justify-between items-start">
        <div>
          <p className="text-md sm:text-[26px] pb-2 font-semibold text-[#1A1A40]">
            Welcome Back
          </p>
          <p className="text-xs sm:text-[14px] text-[#4B5563]">
            We're excited to have you here! This is your personal space <br />
            to track progress, access lessons, and connect with your tutors.
          </p>
        </div>
      </div>

      <div className="my-8">
        <MainCards dashbaordStats={dashbaordStats} />
      </div>

      <div className="w-full text-[#1A1A40] mt-6 md:mt-8">
        <p className="text-[#1A1A40] text-lg md:text-xl font-semibold">
          Recent Classes
        </p>

        <div className="flex flex-col gap-4 xl:flex-row">
          {/* Left Table (Recent Classes) */}
          <div className="w-full xl:w-[55%]">
            <div className="border bg-white p-4 rounded-lg shadow-sm">
              <table className="w-full text-sm sm:text-base">
                <thead className="text-left text-[#1A1A40] border-b text-md sm:text-[18px] font-medium border-gray-200">
                  <tr className="text-xs sm:text-[14px]">
                    <th className="pr-10">Tutor</th>
                    <th>Date</th>
                    <th>Time</th>
                  </tr>
                </thead>
                <tbody className="text-left">
                  {isLoading ? (
                    <tr>
                      <td
                        colSpan="4"
                        className="py-4 text-center text-[#4B5563]"
                      >
                        Loading...
                      </td>
                    </tr>
                  ) : recentClasses.length === 0 ? (
                    <tr>
                      <td
                        colSpan="4"
                        className="py-4 text-center text-[#4B5563]"
                      >
                        No recent classes found.
                      </td>
                    </tr>
                  ) : (
                    recentClasses.map((cls) => (
                      <tr
                        key={cls.id}
                        className="border-b text-xs sm:text-[14px] text-[#4B5563] border-gray-100"
                      >
                        <td className="py-4">
                          {capitalizeWords(cls.tutor.name)}
                        </td>
                        <td>{cls.formattedDate}</td>
                        <td className="mx-2">
                          {formatDisplayTime(cls.scheduledTime)}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Right Sidebar (Upcoming Classes) */}
          <div className="w-full xl:w-[45%]">
            <div className="bg-white rounded-lg border shadow-sm p-4">
              <h2 className="text-[#1A1A40] text-base sm:text-lg md:text-[22px] font-semibold mb-2 sm:mb-3">
                Upcoming Classes
              </h2>
              <div className="flex gap-2 justify-between text-center mt-4 mb-4">
                {dates.map((d) => (
                  <div
                    key={d.date}
                    className="flex flex-col items-center cursor-pointer"
                    onClick={() => setActiveDate(d.date)}
                  >
                    <span className="text-xs text-gray-500">{d.day}</span>
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center mt-1 text-sm ${
                        d.date === activeDate
                          ? "bg-green-100 text-green-700 font-semibold"
                          : "text-gray-500"
                      }`}
                    >
                      {d.date}
                    </div>
                  </div>
                ))}
              </div>
              <hr />

              {/* Upcoming classes list */}
              <div className="mt-6 space-y-4">
                {isLoading ? (
                  <p className="text-center text-[#4B5563]">
                    Loading upcoming classes...
                  </p>
                ) : upcomingClasses.length === 0 ? (
                  <p className="text-center text-[#4B5563]">
                    No classes scheduled for this day
                  </p>
                ) : (
                  upcomingClasses.map((cls) => {
                    const startTime = formatDisplayTime(cls.scheduledTime);
                    const endTime = getEndTime(cls.scheduledTime, cls.duration);

                    return (
                      <div key={cls.id} className="flex items-center">
                        {/* Time slot */}
                        <div className="flex flex-col items-center min-w-[80px] text-sm">
                          <p className="font-medium text-gray-700">
                            {startTime}
                          </p>
                          <p className="text-xs text-gray-500">to</p>
                          <p className="font-medium text-gray-700">{endTime}</p>
                        </div>

                        <div className="flex w-full items-center gap-4 p-4 rounded-lg border border-amber-200 bg-amber-50 shadow-sm">
                          <div className="flex-1 flex items-center gap-3">
                            <div className="relative">
                              <img
                                alt="tutor image"
                                src={cls.tutor?.image || userVector}
                                className="rounded-full w-12 h-12 object-cover border-2 border-white shadow-sm"
                              />
                              <div className="absolute -bottom-1 -right-1 bg-green-500 rounded-full p-1">
                                <Video className="h-3 w-3 text-white" />
                              </div>
                            </div>
                            <div>
                              <h3 className="font-medium text-gray-900">
                                {capitalizeWords(cls.tutor.name)}
                              </h3>
                              <p className="text-sm text-[#4B5563]">
                                {cls.tutor.subjects[0]?.title || "N/A"}
                              </p>
                            </div>
                          </div>

                          <button
                            className="bg-primary text-white px-4 py-2 rounded-md text-sm font-medium transition-colors shadow-sm"
                            onClick={() =>
                              navigate(`/classroom/${cls.classroom.id}`, {
                                state: cls,
                              })
                            }
                          >
                            Join Class
                          </button>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
