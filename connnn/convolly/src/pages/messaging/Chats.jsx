import React, { useCallback, useEffect, useRef, useState } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import ChatRoom from "./components/ChatRoom";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import dayjs from "dayjs";
import userVector from "@/assets/svgs/userVector.svg";
import { useGetUserConversations } from "@/api/chat";
import { useGetUserProfile } from "@/api/user";
import useInfiniteScroll from "@/hooks/useInfiniteScroll";
import { decryptMessage } from "./utils";
import Loader from "@/components/loader/loader";
import { safelyBind } from "@/utils/event";
import { CHAT_CONVERSATION } from "@/constants";
import useChatProvider from "@/hooks/useChatProvider";

export const CONVERSATION_PARAM_KEY = "chat_cid";

const Chats = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [chats, setChats] = useState([]);
  const [conversationSelected, setConversationSelected] = useState(false);

  const currentUser = useSelector((state) => state?.app?.userInfo?.user);

  const conversationId = searchParams.get(CONVERSATION_PARAM_KEY) || "";

  const receiverRole = searchParams.get("role") || undefined;

  const stateRef = useRef({
    ignoreChats: false,
    selected: !!userId,
  });

  const { socketIsConnected, socket } = useChatProvider();

  const { data: otherUser, isFetching: isFetchingUser } = useGetUserProfile(
    userId,
    receiverRole
  );

  const { data: conversations, isFetching: isFetchingConversations } =
    useInfiniteScroll(useGetUserConversations(currentUser?.id));

  const normalizeToChat = useCallback(
    async (chat) => ({
      ...chat,
      otherUser:
        chat.otherUser ||
        Object.values(chat.participants).filter(
          (u) => u.id !== currentUser.id
        )[0],
      lastMessageDate: chat.lastMessage?.createdAt || chat.updatedAt,
      ...(await decryptMessage(currentUser, chat.lastMessage)),
    }),
    [currentUser]
  );

  useEffect(() => {
    (async () => {
      if (stateRef.current.ignoreChats) return;

      let chats = [];

      if (conversations)
        for (const chat of conversations) {
          chats.push(await normalizeToChat(chat));
        }

      //http://localhost:5173/student/messages/687e36cb7091cf8dfb57dad8?chat_cid=687fc4c01e469ed91d43b6d2

      if (otherUser) {
        if (!chats.find((chat) => !!chat.participants[otherUser.id])) {
          chats = [
            {
              participants: { [otherUser.id]: otherUser },
              otherUser,
              text: "Start a new conversation",
              lastMessageDate: new Date().toISOString(),
            },
            ...chats,
          ];
        }

        setConversationSelected(true);
      }

      setChats(chats);
    })();
  }, [currentUser, otherUser, conversations, normalizeToChat, setSearchParams]);

  useEffect(() => {
    console.log(socket?.connected, socket?.id);

    if (socket) {
      const updateChat = async (conversation) => {
        console.log(conversation);

        stateRef.current.ignoreChats = true;

        let newChats = [];

        let isBool = false;

        for (const chat of chats) {
          const newChat = await normalizeToChat(conversation);

          isBool =
            isBool ||
            chat.id === conversation.id ||
            chat.otherUser.id === newChat.otherUser.id;

          newChats.push(isBool ? newChat : chat);
        }

        if (!isBool)
          newChats = [await normalizeToChat(conversation), ...newChats];

        if (stateRef.current.selected)
          setSearchParams(
            (params) => {
              params.set(CONVERSATION_PARAM_KEY, conversation.id);
              return params;
            },
            { replace: true }
          );

        setChats(newChats);
      };

      safelyBind(socket, CHAT_CONVERSATION, updateChat);
    }
  }, [socket, chats, normalizeToChat, setSearchParams, socketIsConnected]);

  const handleChatClick = (chat) => {
    setConversationSelected(true);

    navigate(
      `/${currentUser?.role}/messages/${
        chat.otherUser.id
      }?${CONVERSATION_PARAM_KEY}=${chat.id || ""}`
    );
  };

  const isFetching =
    isFetchingUser || isFetchingConversations || !socketIsConnected;

  if (isFetching) return <Loader fullscreen />;

  const withConversation = conversationId || conversationSelected;

  console.log(conversationSelected, conversationId);

  return (
    <div className="w-full h-full">
      <div className="w-full sm:flex h-full gap-5">
        <div className="w-full border rounded-md bg-gray-50 overflow-hidden sm:max-w-[40%]">
          <div className="overflow-y-auto h-full">
            {isFetching ? (
              <p className="text-gray-500 text-center p-4">
                Loading conversations...
              </p>
            ) : chats.length > 0 ? (
              chats.map((chat, i) => {
                return (
                  <div
                    key={i}
                    onClick={() => handleChatClick(chat)}
                    className={`p-3 cursor-pointer hover:bg-gray-100 flex items-center gap-3 border-b ${
                      chat.otherUser.id === otherUser?.id
                        ? "bg-[#EBEDF0]"
                        : "bg-white"
                    }`}
                  >
                    <img
                      src={chat.otherUser.image || userVector}
                      alt={chat.otherUser.firstname}
                      className="w-10 h-10 rounded-full object-cover"
                    />

                    <div className="w-full">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium truncate">
                            {chat.otherUser
                              ? `${chat.otherUser.firstname} ${chat.otherUser.lastname}`
                              : "Unknown User"}
                          </p>

                          <p className="">{chat.text}</p>
                        </div>

                        <span className="text-xs text-gray-500 whitespace-nowrap">
                          {dayjs(chat.lastMessageDate).format("MMM D")}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-gray-500 text-center p-4">
                Start a conversation with someone
              </p>
            )}
          </div>
        </div>

        <div className="w-full sm:max-w-[60%]">
          {withConversation ? (
            <ChatRoom conversationId={conversationId} otherUser={otherUser} />
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">
                {chats.length
                  ? "Select a chat and continue where you left off."
                  : "Start a conversation"}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Chats;
