import {
  decryptFile,
  decryptText,
  encryptFile,
  encryptText,
} from "@/utils/crypto";

export const decryptMessage = async (currentUser, msg) => {
  if (!msg) return;

  const text = await decryptText(
    currentUser.privateKey,
    msg.recipients[currentUser.id]
  );

  const files = [];

  for (const file of msg.files) {
    files.push(await decryptRecipientFile(currentUser, file));
  }

  return { text, files };
};

export const decryptRecipientFile = async (
  receiver,
  fileMessage,
  type = "blob"
) => {
  const decrypted = await decryptFile(
    fileMessage.recipients[receiver.id],
    receiver.privateKey
  );

  const blob = new Blob([decrypted], { type: fileMessage.mimetype });

  const file =
    type === "blob"
      ? blob
      : new File([blob], `${fileMessage.name}.${fileMessage.extention}`, {
          type: fileMessage.mimetype,
          lastModified: Date.now(),
        });

  return file;
};

export const serializeToChatMessage = async (
  sender,
  receiver,
  opts = {
    files: undefined,
    message: undefined,
    conversationId: undefined,
  }
) => {
  const { message, files = [], conversationId } = opts;

  const encryptedFiles = [];

  for (const { file } of files) {
    const recipients = {};

    recipients[receiver.id] = await encryptFile(
      file,
      receiver.encryptedData.publicKey
    );

    recipients[sender.id] = await encryptFile(
      file,
      sender.encryptedData.publicKey
    );

    encryptedFiles.push({
      recipients,
      name: new Date().getTime().toString(),
      extention: file.name.split(".").pop(),
      mimetype: file.type,
      size: file.size,
    });
  }

  const receiverEncryptedMessage = message
    ? await encryptText(receiver?.encryptedData?.publicKey, message)
    : null;

  const senderEncryptedMessage = message
    ? await encryptText(sender?.encryptedData?.publicKey, message)
    : null;

  const tempId = new Date().getTime();

  const chatMessage = {
    conversationId,
    tempId,
    sender: {
      role: sender?.role,
      id: sender?.id,
    },
    receiver: {
      role: receiver?.role,
      id: receiver?.id,
    },
    files: encryptedFiles,
    recipients: {
      [sender?.id]: senderEncryptedMessage,
      [receiver?.id]: receiverEncryptedMessage,
    },
    text: message || (encryptedFiles.length > 0 ? "[File attached]" : ""),
  };

  return chatMessage;
};
