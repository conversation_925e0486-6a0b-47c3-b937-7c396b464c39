import React, { useEffect, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";

import { SelectIcon } from "@radix-ui/react-select";
import { Button } from "@/components/button/button";
import { CustomSelect } from "@/components/select/select";
import { useSelector, useDispatch } from "react-redux";
import { handleProfileUpdateResponse } from "@/utils/profileUtils";
import PhoneInputWithCountry from "@/components/inputs/phoneInputWithCountry";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { Trash2 } from "lucide-react";
import InputField from "@/components/inputs";
import Loader from "@/components/loader/loader";
import { convertToBase64 } from "@/utils";
import tutorAvatar from "@/assets/images/tutorAvatar.png";

import { toast } from "react-toastify";

const TutorProfileSettings = () => {
  const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);
  const instructorDetails = useSelector((state) => state?.app?.userInfo?.user);
  const dispatch = useDispatch();

  const [countries, setCountries] = useState([]);
  const [languages, setLanguages] = useState([]);
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [loadingLanguages, setLoadingLanguages] = useState(false);

  const [previewImage, setPreviewImage] = useState(null);

  const {
    register,
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      languages: [{ name: "", level: "Native" }],
      teachingSubjects: [{ title: "" }],
      timezone: instructorDetails?.timezone,
    },
  });

  const { fields, remove } = useFieldArray({
    control,
    name: "languages",
  });

  const levelOptions = [
    { value: "A1", label: "Beginners (CEFR-level A1)" },
    { value: "A2", label: "Pre-intermediate (CEFR-level A2)" },
    { value: "B1", label: "Intermediate (CEFR-level-B1)" },
    { value: "B2", label: "Upper-intermediate (CEFR-level-B2)" },
    { value: "C1", label: "Advanced (CEFR-level- C1)" },
    { value: "C2", label: "Proficiency (CEFR-level-C2)" },
    { value: "Native", label: "Native" },
  ];

  const timezones = [
    { value: "Africa/Lagos", label: "Lagos (UTC+1)" },
    { value: "America/New_York", label: "New York (UTC-5/-4)" },
    { value: "America/Los_Angeles", label: "Los Angeles (UTC-8/-7)" },
    { value: "Europe/London", label: "London (UTC+0/+1)" },
    { value: "Asia/Kolkata", label: "Kolkata (UTC+5:30)" },
    { value: "Asia/Tokyo", label: "Tokyo (UTC+9)" },
    { value: "Australia/Sydney", label: "Sydney (UTC+10/+11)" },
    { value: "Europe/Paris", label: "Paris (UTC+1/+2)" },
    { value: "Europe/Berlin", label: "Berlin (UTC+1/+2)" },
    { value: "Africa/Cairo", label: "Cairo (UTC+2)" },
    { value: "America/Chicago", label: "Chicago (UTC-6/-5)" },
    { value: "America/Denver", label: "Denver (UTC-7/-6)" },
    { value: "America/Sao_Paulo", label: "São Paulo (UTC-3)" },
    { value: "Asia/Dubai", label: "Dubai (UTC+4)" },
    { value: "Asia/Shanghai", label: "Shanghai (UTC+8)" },
    { value: "Asia/Singapore", label: "Singapore (UTC+8)" },
    { value: "Europe/Moscow", label: "Moscow (UTC+3)" },
    { value: "Africa/Johannesburg", label: "Johannesburg (UTC+2)" },
    { value: "Pacific/Auckland", label: "Auckland (UTC+12/+13)" },
    { value: "America/Toronto", label: "Toronto (UTC-5/-4)" },
  ];

  const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
    useUpdateProfileMutation
  );

  const updateProfile = async (data) => {
    try {
      // Prepare the payload
      const payload = {
        ...data,
        // Handle timezone object/string conversion
        timezone: data.timezone?.value || data.timezone,
        image: previewImage,
        userId: tutorId,
        role: "tutor",
      };

      // Clean up unnecessary fields
      delete payload.__v;
      delete payload.password;

      console.log("Payload being sent to API:", payload);

      const res = await handleUpdateTutor(payload);

      // Update Redux store with new user data including image
      handleProfileUpdateResponse(dispatch, instructorDetails, res);

      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    }
  };

  // Fetch countries from REST Countries API
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        setLoadingCountries(true);
        const response = await fetch(
          "https://restcountries.com/v3.1/all?fields=name"
        );
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        const countryOptions = data
          .map((country) => ({
            value: country.name.common,
            label: country.name.common,
          }))
          .sort((a, b) => a.label.localeCompare(b.label));
        setCountries(countryOptions);
      } catch (error) {
        console.error("Error fetching countries:", error.message);
        setCountries([{ value: "", label: "Unable to load countries" }]);
      } finally {
        setLoadingCountries(false);
      }
    };

    fetchCountries();
  }, []);

  // Fetch languages from REST Countries API
  useEffect(() => {
    const fetchLanguages = async () => {
      try {
        setLoadingLanguages(true);
        const response = await fetch(
          "https://restcountries.com/v3.1/all?fields=languages"
        );
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        const languageSet = new Set();
        data.forEach((country) => {
          if (country.languages) {
            Object.values(country.languages).forEach((lang) =>
              languageSet.add(lang)
            );
          }
        });
        const languageOptions = Array.from(languageSet)
          .map((lang) => ({
            value: lang,
            label: lang,
          }))
          .sort((a, b) => a.label.localeCompare(b.label));
        setLanguages(languageOptions);
      } catch (error) {
        console.error("Error fetching languages:", error.message);
        setLanguages([{ value: "", label: "Unable to load languages" }]);
      } finally {
        setLoadingLanguages(false);
      }
    };

    fetchLanguages();
  }, []);

  useEffect(() => {
    if (instructorDetails) {
      const currentTimezone = timezones.find(
        (tz) => tz.value === instructorDetails.timezone
      );

      reset({
        firstname: instructorDetails?.firstname || "",
        lastname: instructorDetails?.lastname || "",
        email: instructorDetails?.email || "",
        countryOfBirth: instructorDetails?.countryOfBirth || "",
        phone: instructorDetails?.phone || "",
        timezone: currentTimezone || instructorDetails?.timezone || "",
        teachingSubjects:
          instructorDetails?.teachingSubjects?.length > 0
            ? instructorDetails?.teachingSubjects
            : [{ title: "" }],
        languages: instructorDetails?.languages?.length
          ? instructorDetails?.languages
          : [{ name: "", level: "Native" }],
      });
      setPreviewImage(instructorDetails?.image);
    }
  }, [instructorDetails, reset, timezones]);

  return (
    <div className="md:max-w-[528px] w-auto">
      {(loadingCountries || loadingLanguages) && <Loader />}

      <form
        onSubmit={handleSubmit(updateProfile)}
        className="flex text-[18px] flex-col w-auto "
      >
        <div className="flex gap-4">
          <img
            src={previewImage || tutorAvatar}
            alt="Tutor Preview"
            className="w-full sm:max-w-[170px] max-w-[100px] h-full max-h-[150px] object-cover rounded-md border"
          />

          <div className="flex flex-col items-center">
            <div className="flex flex-col ">
              <label className="px-6 py-2 border rounded-md cursor-pointer">
                Upload Photo
                <input
                  type="file"
                  aria-label="upload a picture"
                  id="upload-image"
                  {...register("image", {
                    required: !previewImage ? "Please upload an image" : false,
                    onChange: async (e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        const base64 = await convertToBase64(file);
                        setPreviewImage(base64);
                      }
                    },
                  })}
                  accept="image/png, image/jpeg"
                  className="hidden"
                />
              </label>
            </div>
            <p className="text-[#4B5563] text-sm">
              Maximum size – 2MB <br />
              JPG or PNG format
            </p>
          </div>
        </div>

        <div className="text-[#1A1A40]  mt-6 space-y-4">
          <InputField
            label="First name"
            register={register}
            fieldName="firstname"
            placeHolder="Enter your first name"
            isRequired={true}
            error={errors?.firstname?.message}
          />

          <InputField
            label="Last name"
            register={register}
            fieldName="lastname"
            placeHolder="Enter your last name"
            isRequired={true}
            error={errors?.lastname?.message}
          />

          <InputField
            label="Email"
            register={register}
            fieldName="email"
            fieldType="email"
            disabled
            placeHolder="Enter your email address"
            isRequired={true}
            error={errors?.email?.message}
          />

          <CustomSelect
            label="Country of birth"
            options={countries}
            placeholder="Select country"
            className="p-5 py-[22px]"
            parentClassName="mb-7"
            name="countryOfBirth"
            control={control}
            isRequired={true}
            error={errors?.countryOfBirth?.message}
          />

          <div className="mb-7">
            {fields?.map((item, index) => (
              <div key={item.id} className="relative">
                <div className="flex sm:gap-5 gap-2 items-baseline mt-7">
                  <CustomSelect
                    placeholder="Select language"
                    label="Language you speak"
                    options={languages}
                    control={control}
                    name={`languages.${index}.name`}
                    isRequired={true}
                    error={errors?.languages?.[index]?.name?.message}
                    className="p-5 sm:py-[24px] py-[22px]"
                    disabled={loadingLanguages}
                  />

                  <CustomSelect
                    placeholder="Select level"
                    label="Level"
                    options={levelOptions}
                    control={control}
                    name={`languages.${index}.level`}
                    isRequired={true}
                    error={errors?.languages?.[index]?.level?.message}
                    className="p-5 sm:py-[24px] py-[22px]"
                  />

                  {index > 0 && (
                    <button
                      type="button"
                      onClick={() => remove(index)}
                      className="p-1 rounded-full h-8 w-8 mt-auto mb-[10px] flex justify-center items-center shrink-0 bg-red-100 hover:bg-red-200"
                      title="Remove language"
                    >
                      <Trash2 size={16} className="text-red-500" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>

          <CustomSelect
            placeholder="Select your timezone"
            label="Time zone"
            options={timezones}
            control={control}
            name="timezone"
            isRequired={true}
            error={errors?.timezone?.message}
            className="p-5 py-[22px]"
            parentClassName="mb-7"
          />

          <p className="text-secondary mb-3 max-sm:text-sm mt-7">
            Phone number
          </p>

          <PhoneInputWithCountry
            control={control}
            register={register}
            name="phone"
            isRequired={false}
            error={errors.phone?.message}
          />
        </div>

        <Button
          className="w-full mt-4 h-[50px] mb-3"
          type="submit"
          disabled={updating}
        >
          {updating ? <Loader size={24} /> : "Save Changes"}
        </Button>
      </form>
    </div>
  );
};

export default TutorProfileSettings;
