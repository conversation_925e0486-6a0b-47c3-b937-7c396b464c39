import { useEffect, useState } from "react";
import useSocket, { useSocketStore } from "./useSocket";
import { useSelector } from "react-redux";
import { safelyBind } from "@/utils/event";
import { serializeToChatMessage } from "@/pages/messaging/utils";
import {
  CHAT_MESSAGE_SAVED,
  CHAT_SEND_MESSAGE,
  SOCKET_ERROR_KEY,
} from "@/constants";

const useChatProvider = () => {
  const api = useSocket();

  useEffect(() => {
    if (api.socket) {
      api.socket.emit("join-chat-room");

      const onError = (err) => {
        console.log("socket error", err);
      };

      safelyBind(api.socket, SOCKET_ERROR_KEY, onError);

      return () => {
        api.socket.off(SOCKET_ERROR_KEY, onError);
      };
    }
  }, [api.socket]);

  return api;
};

export const useChatHook = (opts) => {
  const { socket } = useSocketStore();

  const [loading, setLoading] = useState(false);

  const currentUser = useSelector((state) => state?.app?.userInfo?.user);

  useEffect(() => {
    if (socket) {
      const { onChatSaved } = opts || {};

      const onSaved = (message, tempId) => {
        setLoading(false);
        onChatSaved?.(message, tempId);
      };

      safelyBind(socket, CHAT_MESSAGE_SAVED, onSaved);

      return () => {
        socket.off(CHAT_MESSAGE_SAVED, onSaved);
      };
    }
  }, [socket, opts]);

  const sendChatMessage = async (chat) => {
    setLoading(true);

    const message = await serializeToChatMessage(
      currentUser,
      opts?.receiver,
      chat || opts?.chat
    );

    socket.emit(CHAT_SEND_MESSAGE, message);

    return message;
  };

  return { socket, currentUser, sendChatMessage, loading };
};

export default useChatProvider;
