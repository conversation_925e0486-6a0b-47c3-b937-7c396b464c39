import { handleServerError } from "@/api/utils";
import { SERVER_ORIGIN, SOCKET_ERROR_KEY } from "@/constants";
import { safelyBind } from "@/utils/event";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { io } from "socket.io-client";
import { create } from "zustand";

export const useSocketStore = create((set, get) => ({
  socket: null,
  socketIsConnected: false,
  socketIsConnecting: false,

  connectSocket: (token, opts = {}) => {
    if (!token) return;

    const _socket = get().socket;

    if (_socket?.connected) return;

    let socket =
      _socket ||
      io(SERVER_ORIGIN, {
        auth: { token },
        transports: ["websocket"],
      });

    socket.on("connect", () => {
      set({ socketIsConnected: true, socketIsConnecting: false });
      opts.onConnect?.(socket);
    });

    socket.on("disconnect", () => {
      set({ socketIsConnected: false });
      opts.onDisconnect?.();
    });

    socket.on("connect_error", (err) => {
      handleServerError(err.data);
    });

    const onError = (err) => {
      console.log("socket", err);
    };

    safelyBind(socket, SOCKET_ERROR_KEY, onError);

    socket = socket.connect();

    set({ socket });
  },

  disconnectSocket: () => {
    const { socket, socketIsConnected } = get();

    if (socket && socketIsConnected) {
      socket.disconnect();
      socket.off();
      set({ socket: null, socketIsConnected: false });
    }
  },

  reconnect: () => {
    let socket = get().socket;

    if (socket && !socket.connected) {
      set({ socketIsConnecting: true });

      socket = socket.connect();

      socket.on("connect", () => {
        console.log("connected...");
        set({ socketIsConnected: true, socketIsConnecting: false, socket });
      });

      return socket;
    }
  },
}));

const useSocket = () => {
  const token = useSelector((state) => state?.app?.userInfo?.accessToken);

  const api = useSocketStore();

  useEffect(() => {
    if (token) {
      api.connectSocket(token);
    }

    return () => api.disconnectSocket();
  }, [token, api.connectSocket, api.disconnectSocket]);

  return api;
};

export default useSocket;
