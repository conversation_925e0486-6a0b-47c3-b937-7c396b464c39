import { useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { useScrollAnchor } from "./useScrollAnchor";

const useInfiniteScroll = (api, opts) => {
  const { scrollRef, selector = ".scroll-target" } = opts || {};

  const { ref, inView } = useInView();

  useScrollAnchor(scrollRef, api.data.length, selector);

  useEffect(() => {
    if (inView && api.hasMore) {
      api.fetchNextPage();
    }
  }, [inView, api.hasMore, api.fetchNextPage]);

  api.viewRef = ref;
  api.inView = inView;

  return api;
};

export default useInfiniteScroll;
