import axiosInstance from "../axiosInstance";

export const getUserConversations = async (userId, cursor) => {
  const res = await axiosInstance.post(`/chat/user-conversations/${userId}`, {
    cursor,
  });

  return res.data;
};

export const getConversationMessages = async (
  conversationId,
  userId,
  cursor
) => {
  const res = await axiosInstance.post(
    `/chat/conversation-messages/${conversationId}/${userId}`,
    { cursor, limit: 100 }
  );

  return res.data;
};
