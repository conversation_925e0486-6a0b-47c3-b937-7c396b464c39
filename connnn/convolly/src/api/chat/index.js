import { getConversationMessages, getUserConversations } from "./apiFunction";
import useInfiniteQuery from "@/hooks/useInfiniteQuery";

export const useGetUserConversations = (userId) => {
  return useInfiniteQuery({
    queryKey: ["get-user-conversations"],
    queryFn: (pagination) => {
      return userId
        ? getUserConversations(userId, pagination?.nextCursor)
        : new Promise(() => undefined);
    },
    enabled: !!userId,
  });
};

export const useGetConversationMessages = (conversationId, userId) => {
  return useInfiniteQuery({
    backward: true,
    queryKey: ["conversation-messages", conversationId],
    queryFn: (pagination) => {
      return conversationId && userId
        ? getConversationMessages(
            conversationId,
            userId,
            pagination?.nextCursor
          )
        : new Promise(() => undefined);
    },
    enabled: !!(conversationId && userId),
  });
};
