import { toast } from "react-toastify";
import { getCurrentFromPath } from "@/utils/history";
import { SEARCH_PARAMS_FROM_PATH } from "@/constants";
import { router } from "@/_config/inAppUrl";

export const getNextFetchParam = (param) => {
  if (param) {
    const {
      details: {
        pagination: { nextCursor },
      },
    } = param;
    return nextCursor;
  }

  return;
};

let lastToastMessage = null;
let toastTimeout;

export const handleUnauthorizedError = (
  message = "Session expired, please log in again."
) => {
  if (lastToastMessage === message) return;
  lastToastMessage = message;

  toast.error(message, { autoClose: 3000 });

  clearTimeout(toastTimeout);
  toastTimeout = setTimeout(() => (lastToastMessage = null), 5000);

  if (window.location.pathname !== "/signin") {
    router.navigate(
      `/signin?${SEARCH_PARAMS_FROM_PATH}=${getCurrentFromPath()}`
    );
  }
};

export const handleServerError = (error) => {
  switch (error?.status) {
    case 401:
      handleUnauthorizedError();
      break;
    default:
      break;
  }
};
