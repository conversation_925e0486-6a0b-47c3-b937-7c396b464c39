import { API_URL } from "@/constants";
import { getStorage } from "@/utils/storage";
import axios from "axios";
import { handleServerError } from "./utils";

const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    const token =
      getStorage("userInfo")?.accessToken ||
      getStorage("currentUser")?.accessToken ||
      "";

    if (token && !config.headers["Authorization"]) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

axiosInstance.interceptors.response.use(
  (res) => res,
  (error) => {
    handleServerError(error.response.data);
    return Promise.reject(error);
  }
);

export default axiosInstance;
